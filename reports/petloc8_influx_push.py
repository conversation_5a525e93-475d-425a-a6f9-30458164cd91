import pymysql
from influxdb import InfluxDBClient
from datetime import datetime

# === MySQL Configuration ===
MYSQL_CONFIG = {
    "host": "loc8-aurora-instance-1.crqgu222g0za.eu-central-1.rds.amazonaws.com",
    "user": "trackimo",
    "password": "Daniel81395",
    "database": "trackimo",
    "cursorclass": pymysql.cursors.DictCursor
}

# === InfluxDB Configuration ===
INFLUX_CONFIG = {
    "host": "***********",
    "port": 8086,
    "username": "superadmin",
    "password": "dizzyC@nary96",
    "database": "watchinu_production"
}

# === SQL Queries ===

TOTAL_ACTIVE_QUERY = """
SELECT COUNT(*) AS total_active
FROM devices d
JOIN devices_states ds ON d.id = ds.device_id
JOIN device_additional_details dad ON d.id = dad.device_id
WHERE d.status = true
  AND d.account_id > 0
  AND d.user_id > 0
  AND ds.fw_version LIKE '%W217_VPET_E_M%'
  AND dad.btFWVersion LIKE 'CAT1_BT%';
"""

COMMUNICATING_DEVICES_QUERY = """
SELECT COUNT(DISTINCT d.id) AS communicating_count
FROM devices d
JOIN devices_states ds ON d.id = ds.device_id
JOIN device_additional_details dad ON d.id = dad.device_id
JOIN devices_last_location dll ON d.id = dll.device_id
WHERE d.status = true 
  AND d.account_id > 0
  AND d.user_id > 0
  AND dll.updated > NOW() - INTERVAL 30 DAY
  AND ds.fw_version LIKE '%W217_VPET_E_M%'
  AND dad.btFWVersion LIKE 'CAT1_BT%';
"""

FIRMWARE_COUNT_QUERY = """
SELECT COUNT(us_fw) AS count
FROM (
    SELECT d.id,
           RIGHT(ds.fw_version, 13) AS us_fw,
           dad.btFWVersion AS bt_fw
    FROM devices d
    JOIN devices_states ds ON d.id = ds.device_id
    JOIN device_additional_details dad ON dad.device_id = d.id
    JOIN devices_last_location dll ON d.id = dll.device_id
    WHERE dll.updated > NOW() - INTERVAL 30 DAY
      AND d.brand = 24
      AND ds.fw_version LIKE '%20250829_1107%' 
      AND dad.btFWVersion LIKE '%CAT1_BT_V018_20250805%'
      AND dad.wifiFWVersion LIKE '%CAT1_WIFI_V014_20250830%'
    GROUP BY d.id
) AS t2
GROUP BY us_fw, bt_fw
ORDER BY count DESC;
"""

FIRMWARE_COMMUNICATING_QUERY = """
SELECT COUNT(DISTINCT d.id) AS count
FROM devices d
JOIN devices_states ds ON d.id = ds.device_id
JOIN device_additional_details dad ON d.id = dad.device_id
JOIN devices_last_location dll ON d.id = dll.device_id
WHERE d.status = true 
  AND d.account_id > 0
  AND d.user_id > 0
  AND dll.updated > NOW() - INTERVAL 30 DAY
  AND ds.fw_version LIKE '%20250829_1107%' 
  AND dad.btFWVersion LIKE '%CAT1_BT_V018_20250805%'
  AND dad.wifiFWVersion LIKE '%CAT1_WIFI_V014_20250830%';
"""

# === Utility Functions ===

def fetch_single_value(query, key):
    connection = pymysql.connect(**MYSQL_CONFIG)
    try:
        with connection.cursor() as cursor:
            cursor.execute(query)
            result = cursor.fetchone()
            return result[key]
    finally:
        connection.close()

def push_to_influx(firmware_count, total_active, communicating_count, firmware_comm_count):
    client = InfluxDBClient(**INFLUX_CONFIG)
    current_time = datetime.utcnow().isoformat()

    pct_active = round((firmware_count / total_active) * 100, 2) if total_active else 0.0
    pct_communicating = round((firmware_comm_count / communicating_count) * 100, 2) if communicating_count else 0.0

    json_point = {
        "measurement": "petloc8_firmware_count",
        "fields": {
            "firmware_count": firmware_count,
            "total_active": total_active,
            "communicating_count": communicating_count,
            "percentage_active": pct_active,
            "percentage_communicating": pct_communicating
        },
        "time": current_time
    }

    client.write_points([json_point])
    print(f"Pushed firmware_count = {firmware_count}, total_active = {total_active}, "
          f"communicating = {communicating_count}, firmware_comm = {firmware_comm_count}, "
          f"%active = {pct_active}, %communicating = {pct_communicating} to InfluxDB.")

# === Main ===

def main():
    firmware_count = fetch_single_value(FIRMWARE_COUNT_QUERY, 'count')
    firmware_comm_count = fetch_single_value(FIRMWARE_COMMUNICATING_QUERY, 'count')
    total_active = fetch_single_value(TOTAL_ACTIVE_QUERY, 'total_active')
    communicating_count = fetch_single_value(COMMUNICATING_DEVICES_QUERY, 'communicating_count')

    if firmware_count is not None and firmware_comm_count is not None:
        push_to_influx(firmware_count, total_active, communicating_count, firmware_comm_count)
    else:
        print("No firmware data found.")

if __name__ == "__main__":
    main()
