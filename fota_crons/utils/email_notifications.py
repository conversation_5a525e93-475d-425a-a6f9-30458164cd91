import requests
import json
from datetime import datetime
from typing import List, Dict, Any

# === Email Configuration ===
MANDRILL_API_KEY = '**********************'
SENDER = '<EMAIL>'
PETLOC8_RECIPIENT = '<EMAIL>'

def send_petloc8_fota_notification(device_ids: List[str], job_name: str, fota_type: str) -> bool:
    """
    Send email notification for petloc8 environment FOTA jobs
    
    Args:
        device_ids: List of device IDs that were processed
        job_name: Name of the FOTA job
        fota_type: Type of FOTA (unisoc, bt, wifi, gps)
    
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        device_count = len(device_ids)
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Create HTML email body
        html_body = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f4f4f4; padding: 15px; border-radius: 5px; }}
                .content {{ margin: 20px 0; }}
                .device-list {{ background-color: #f9f9f9; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto; }}
                .footer {{ margin-top: 20px; font-size: 12px; color: #666; }}
                .highlight {{ color: #007bff; font-weight: bold; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h2>🔄 PetLoc8 FOTA Job Notification</h2>
                <p><strong>Job:</strong> {job_name}</p>
                <p><strong>FOTA Type:</strong> {fota_type}</p>
                <p><strong>Time:</strong> {current_time}</p>
            </div>
            
            <div class="content">
                <h3>📊 Summary</h3>
                <p>Total devices processed: <span class="highlight">{device_count}</span></p>
                
                <h3>📱 Device IDs</h3>
                <div class="device-list">
        """
        
        # Add device IDs to the email
        if device_count > 0:
            for i, device_id in enumerate(device_ids, 1):
                html_body += f"<p>{i}. {device_id}</p>"
        else:
            html_body += "<p>No devices were processed in this job.</p>"
        
        html_body += """
                </div>
            </div>
            
            <div class="footer">
                <p>This is an automated notification from the PetLoc8 FOTA system.</p>
                <p>Generated at: """ + current_time + """</p>
            </div>
        </body>
        </html>
        """
        
        # Prepare email payload
        payload = {
            "key": MANDRILL_API_KEY,
            "message": {
                "from_email": SENDER,
                "from_name": "PetLoc8 FOTA System",
                "to": [{"email": PETLOC8_RECIPIENT, "type": "to"}],
                "subject": f"🔄 PetLoc8 FOTA Job Completed - {device_count} devices processed",
                "html": html_body,
                "important": True,
                "track_opens": True,
                "track_clicks": True,
                "auto_text": True,
                "preserve_recipients": False
            }
        }
        
        # Send email via Mandrill API
        response = requests.post(
            "https://mandrillapp.com/api/1.0/messages/send.json", 
            data=json.dumps(payload),
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            response_data = response.json()
            if response_data and len(response_data) > 0 and response_data[0].get('status') == 'sent':
                print(f"✅ PetLoc8 FOTA notification email sent successfully to {PETLOC8_RECIPIENT}")
                return True
            else:
                print(f"❌ Failed to send PetLoc8 FOTA notification email. Response: {response_data}")
                return False
        else:
            print(f"❌ Failed to send PetLoc8 FOTA notification email. Status: {response.status_code}, Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error sending PetLoc8 FOTA notification email: {str(e)}")
        return False

def send_fota_notification_if_petloc8(env: str, device_ids: List[str], job_name: str, fota_type: str) -> bool:
    """
    Send FOTA notification email only if environment is petloc8
    
    Args:
        env: Environment name
        device_ids: List of device IDs that were processed
        job_name: Name of the FOTA job
        fota_type: Type of FOTA (unisoc, bt, wifi, gps)
    
    Returns:
        bool: True if email sent successfully or not petloc8 env, False if failed to send
    """
    if env.lower() == 'petloc8':
        print(f"📧 Sending PetLoc8 FOTA notification for {len(device_ids)} devices...")
        return send_petloc8_fota_notification(device_ids, job_name, fota_type)
    else:
        print(f"ℹ️ Environment is '{env}', skipping PetLoc8 notification")
        return True
